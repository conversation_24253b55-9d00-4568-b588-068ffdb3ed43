# 📱 移动端用户数据隔离使用指南

本文档介绍如何在移动端APP中使用简洁有效的用户数据隔离机制。

## 🎯 设计理念

- **简洁有效**: 只保留移动端真正需要的核心功能
- **易于使用**: 提供简单直观的API接口
- **自动管理**: 自动处理用户上下文和数据隔离
- **性能优化**: 针对移动端优化，减少不必要的开销

## 🔧 核心组件

### 1. AppStorage - 统一存储接口

```dart
// 用户级别数据存储（自动添加用户前缀）
await AppStorage.setUserString('user_preference', 'value');
final preference = await AppStorage.getUserString('user_preference');

// 应用级别数据存储（全局共享）
await AppStorage.setAppBool('first_launch', false);
final isFirstLaunch = await AppStorage.getAppBool('first_launch', defaultValue: true);
```

### 2. UserManager - 简化用户管理

```dart
// 清理当前用户数据
await UserManager.clearCurrentUserData();

// 切换用户（自动清理当前用户数据）
await UserManager.switchUser('13800138002');

// 检查当前用户状态
final hasData = await UserManager.hasCurrentUserData();
final currentUser = UserManager.getCurrentUserPhone();
final hasContext = UserManager.hasUserContext();
```

### 3. UserStorageContext - 用户上下文管理

```dart
// 设置当前用户
UserStorageContext.instance.setCurrentUser('13800138001');

// 获取当前用户
final currentUser = UserStorageContext.instance.currentUserPhone;

// 检查用户上下文
final hasContext = UserStorageContext.instance.hasUserContext;
```

## 📋 使用场景

### 1. 用户登录时

```dart
// 在 AuthProvider 的 login 方法中
Future<void> login(String phone, String smsCode) async {
  try {
    // 执行登录API调用
    final data = await _authService.login(phone, smsCode);
    
    // 保存用户信息
    _user = User.fromJson(data);
    await User.saveUser(_user!);

    // 设置用户上下文并迁移现有数据
    UserStorageContext.instance.setCurrentUser(phone);
    await UserStorageContext.instance.migrateExistingDataToCurrentUser();

    notifyListeners();
  } catch (e) {
    rethrow;
  }
}
```

### 2. 用户登出时

```dart
// 在 AuthProvider 的 logout 方法中
Future<void> logout() async {
  try {
    // 清理用户相关的本地数据
    await UserManager.clearCurrentUserData();
    
    // 清除用户上下文
    UserStorageContext.instance.setCurrentUser(null);
    
    // 清除用户认证信息
    _user = null;
    await User.clearUser();
    
    notifyListeners();
  } catch (e) {
    // 错误处理
  }
}
```

### 3. 数据存储时

```dart
// 在数据模型中使用自动用户隔离
class Device {
  static Future<void> saveDevice(Device device) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserStorageManager.getAutoUserKey('petDevice');
    String jsonString = jsonEncode(device.toJson());
    await asyncPrefs.setString(storageKey, jsonString);
  }

  static Future<Device?> getDevice() async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserStorageManager.getAutoUserKey('petDevice');
    String? jsonString = await asyncPrefs.getString(storageKey);
    if (jsonString != null) {
      return Device.fromJson(jsonDecode(jsonString));
    }
    return null;
  }
}
```

### 4. UI状态存储时

```dart
// 在页面中存储用户相关的UI状态
class DeviceControlScreen extends StatefulWidget {
  // 保存用户相关的开关状态
  Future<void> _saveState(String key, bool value) async {
    await AppStorage.setUserBool(key, value);
  }

  // 加载用户相关的开关状态
  Future<void> _loadSavedStates() async {
    final isMarqueeLightOn = await AppStorage.getUserBool('marquee_light');
    setState(() {
      _isMarqueeLightOn = isMarqueeLightOn;
    });
  }
}
```

## ✅ 最佳实践

### 1. 数据分类

**用户级别数据（需要隔离）**:
- 设备配置信息
- 健康监测数据
- 二维码信息
- 超声波配置
- 用户偏好设置

**应用级别数据（全局共享）**:
- 首次启动标记
- 应用版本信息
- 全局设置选项
- 缓存配置

### 2. 错误处理

```dart
try {
  await AppStorage.setUserString('key', 'value');
} catch (e) {
  // 存储失败不应该阻止应用运行
  AppLogger.error('存储数据失败: $e', error: e);
}
```

### 3. 性能优化

```dart
// 批量操作时，尽量减少频繁的存储调用
final futures = <Future>[];
futures.add(AppStorage.setUserString('key1', 'value1'));
futures.add(AppStorage.setUserString('key2', 'value2'));
futures.add(AppStorage.setUserString('key3', 'value3'));
await Future.wait(futures);
```

## 🔒 安全考虑

### 1. 敏感数据处理

```dart
// 敏感数据（如用户认证信息）使用 FlutterSecureStorage
class User {
  static Future<void> saveUser(User user) async {
    final storage = FlutterSecureStorage();
    await storage.write(key: 'user', value: jsonEncode(user.toJson()));
  }
}
```

### 2. 数据清理

```dart
// 确保用户登出时完全清理数据
await UserManager.clearCurrentUserData();
UserStorageContext.instance.setCurrentUser(null);
```

## 🧪 测试验证

```dart
// 运行数据隔离测试
flutter test test/storage_isolation_test.dart
flutter test test/user_manager_test.dart
flutter test test/app_storage_test.dart

// 运行完整测试套件
flutter test test/data_isolation_test_suite.dart
```

## 📊 监控和调试

### 1. 日志记录

所有存储操作都会自动记录日志，便于调试：

```
[INFO] 用户存储上下文已更新: 13800138001
[INFO] 用户数据已保存: 13800138001_petDevice
[INFO] 当前用户数据已清理: 13800138001
```

### 2. 状态检查

```dart
// 检查当前状态
print('当前用户: ${UserManager.getCurrentUserPhone()}');
print('有用户上下文: ${UserManager.hasUserContext()}');
print('有用户数据: ${await UserManager.hasCurrentUserData()}');
```

## 🚀 迁移指南

### 从直接使用 SharedPreferences 迁移

**之前**:
```dart
final prefs = await SharedPreferences.getInstance();
await prefs.setString('user_data', 'value');
final data = prefs.getString('user_data');
```

**现在**:
```dart
await AppStorage.setUserString('user_data', 'value');
final data = await AppStorage.getUserString('user_data');
```

### 从复杂用户管理迁移

**之前**:
```dart
await UserManager.getAllLocalUsers();
await UserManager.cleanupInactiveUsers(30);
await UserManager.exportUserData(userPhone);
```

**现在**:
```dart
await UserManager.clearCurrentUserData();
await UserManager.switchUser(newUserPhone);
final hasData = await UserManager.hasCurrentUserData();
```

## 💡 常见问题

### Q: 如何处理用户切换？
A: 使用 `UserManager.switchUser(newUserPhone)` 会自动清理当前用户数据并设置新用户上下文。

### Q: 应用级别数据何时使用？
A: 用于不需要用户隔离的全局设置，如首次启动标记、应用配置等。

### Q: 如何确保数据安全？
A: 敏感数据使用 FlutterSecureStorage，普通数据使用 AppStorage 的用户隔离机制。

### Q: 性能如何优化？
A: 避免频繁的小量存储操作，使用批量操作，合理使用缓存。

---

这个简化版本专注于移动端APP的实际需求，提供了简洁有效的用户数据隔离解决方案。

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/user_storage_manager.dart';

/// 健康数据模型，用于表示宠物的健康监测数据。
class HealthData {
  final int running;
  final int walking;
  final int jumping;
  final int stepCount;
  final double temperature;
  final DateTime datetime;

  // 健康数据模型的构造函数，包含数据的唯一标识符、宠物标识符、活动量、温度和时间戳
  HealthData({
    required this.running,
    required this.walking,
    required this.jumping,
    required this.stepCount,
    required this.temperature,
    required this.datetime,
  });
  Map<String, int> get activity => {
        'running': running,
        'walking': walking,
        'jumping': jumping,
      };

  /// 从 JSON 数据创建 HealthData 实例，用于从后端 API 获取健康监测数据
  factory HealthData.fromJson(Map<String, dynamic> json) {
    return HealthData(
      running: json['running']?.toInt() ?? 0,
      walking: json['walking']?.toInt() ?? 0,
      jumping: json['jumping']?.toInt() ?? 0,
      stepCount: json['stepCount']?.toInt() ?? 0,
      temperature: json['temperature']?.toDouble() ?? 37.0,
      datetime: _parseTimestamp(json['datetime']),
    );
  }

  /// 将 HealthData 实例转换为 JSON 格式，用于将健康监测数据发送到后端 API
  Map<String, dynamic> toJson() {
    return {
      'running': running,
      'walking': walking,
      'jumping': jumping,
      'stepCount': stepCount,
      'temperature': temperature,
      'datetime': datetime.toIso8601String(), // 或根据后端要求修改
    };
  }

  /// 辅助方法，解析时间戳
  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp is String) {
      return DateTime.tryParse(timestamp) ?? DateTime.now();
    } else if (timestamp is int) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else {
      return DateTime.now();
    }
  }

  static Future<void> saveHealthData(HealthData healthData,
      {String key = 'healthData'}) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserStorageManager.getAutoUserKey(key);
    String jsonString = jsonEncode(healthData.toJson()); // 将列表转换为JSON字符串
    await asyncPrefs.setString(
        storageKey, jsonString); // 保存字符串到SharedPreferences
  }

  static Future<HealthData?> getHealthData({String key = 'healthData'}) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserStorageManager.getAutoUserKey(key);
    String? jsonString =
        await asyncPrefs.getString(storageKey); // 从SharedPreferences获取字符串
    if (jsonString != null) {
      return HealthData.fromJson(jsonDecode(jsonString));
    }
    return null;
  }
}

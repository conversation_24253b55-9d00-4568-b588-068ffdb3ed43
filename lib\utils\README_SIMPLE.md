# 📱 简洁版用户数据隔离方案

采用您建议的简单方案，在constants.dart中统一管理需要隔离的数据类型。

## 🎯 核心设计

### 1. 在constants.dart中定义数据类型

```dart
class AppConfig {
  // 需要用户隔离的数据类型
  static const List<String> userDataKeys = [
    'petDevice',
    'healthData',
    'qrCode',
    'ultrasonicConfigs',
  ];
}
```

### 2. UserStorage使用常量列表

```dart
class UserStorage {
  /// 清理指定用户的所有数据
  static Future<void> clearUserData(String userPhone) async {
    final asyncPrefs = SharedPreferencesAsync();
    
    for (String baseKey in AppConfig.userDataKeys) {
      final userKey = getUserKey(baseKey, userPhone);
      await asyncPrefs.remove(userKey);
    }
  }
}
```

## ✅ 优势

### 简洁性
- ✅ 只需在一个地方维护数据类型列表
- ✅ 无复杂的注册机制
- ✅ 代码清晰易懂

### 可维护性
- ✅ 添加新数据类型只需在constants.dart中添加一行
- ✅ 所有相关功能自动支持新数据类型
- ✅ 集中管理，不易遗漏

### 性能
- ✅ 编译时常量，无运行时开销
- ✅ 无动态注册逻辑
- ✅ 直接数组遍历，高效简单

## 📋 使用方式

### 1. 添加新的数据模型

当需要添加新的数据模型时：

**步骤1**: 在constants.dart中添加数据键
```dart
static const List<String> userDataKeys = [
  'petDevice',
  'healthData',
  'qrCode',
  'ultrasonicConfigs',
  'newDataModel', // 添加新的数据类型
];
```

**步骤2**: 创建数据模型，使用UserContext
```dart
class NewDataModel {
  static Future<void> saveData(NewDataModel data) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey('newDataModel');
    // ... 保存逻辑
  }
}
```

### 2. 用户生命周期管理

```dart
// 用户登录
UserContext.instance.setCurrentUser('13800138001');
await UserContext.instance.migrateExistingDataToCurrentUser();

// 用户登出
await UserContext.instance.clearCurrentUserData();
UserContext.instance.setCurrentUser(null);

// 用户切换
await UserContext.instance.switchUser('13800138002');
```

### 3. 数据存储

```dart
// 用户级别数据（自动隔离）
await AppStorage.setUserString('preference', 'value');
final preference = await AppStorage.getUserString('preference');

// 应用级别数据（全局共享）
await AppStorage.setAppBool('first_launch', false);
final isFirstLaunch = await AppStorage.getAppBool('first_launch', defaultValue: true);
```

## 🔧 核心组件

### 1. AppConfig (constants.dart)
- 定义需要隔离的数据类型列表
- 其他应用配置常量

### 2. UserStorage
- 使用AppConfig.userDataKeys进行数据操作
- 提供清理、迁移、检查功能

### 3. UserContext
- 管理当前用户上下文
- 提供用户键名生成功能

### 4. AppStorage
- 统一的存储接口
- 自动处理用户隔离

## 📊 当前支持的数据类型

```dart
AppConfig.userDataKeys = [
  'petDevice',        // 设备信息
  'healthData',       // 健康数据
  'qrCode',          // 二维码信息
  'ultrasonicConfigs', // 超声波配置
];
```

## 🚀 扩展示例

### 添加新的"宠物档案"数据类型

**1. 更新constants.dart**
```dart
static const List<String> userDataKeys = [
  'petDevice',
  'healthData',
  'qrCode',
  'ultrasonicConfigs',
  'petProfile', // 新增
];
```

**2. 创建PetProfile模型**
```dart
class PetProfile {
  static Future<void> savePetProfile(PetProfile profile) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey('petProfile');
    String jsonString = jsonEncode(profile.toJson());
    await asyncPrefs.setString(storageKey, jsonString);
  }

  static Future<PetProfile?> getPetProfile() async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey('petProfile');
    String? jsonString = await asyncPrefs.getString(storageKey);
    if (jsonString != null) {
      return PetProfile.fromJson(jsonDecode(jsonString));
    }
    return null;
  }
}
```

**完成！** 新数据类型自动支持：
- ✅ 用户隔离
- ✅ 数据清理
- ✅ 数据迁移
- ✅ 用户切换

## 🧪 测试

```dart
test('新数据类型自动隔离', () async {
  // 用户1保存数据
  UserContext.instance.setCurrentUser('13800138001');
  await PetProfile.savePetProfile(profile1);

  // 用户2保存数据
  UserContext.instance.setCurrentUser('13800138002');
  await PetProfile.savePetProfile(profile2);

  // 验证数据隔离
  UserContext.instance.setCurrentUser('13800138001');
  final retrievedProfile1 = await PetProfile.getPetProfile();
  expect(retrievedProfile1?.name, equals(profile1.name));
});
```

---

这个方案完全按照您的建议，简单、直接、有效！只需要在constants.dart中维护一个列表，就能实现完整的用户数据隔离功能。

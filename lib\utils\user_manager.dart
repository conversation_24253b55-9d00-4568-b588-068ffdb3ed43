import 'user_storage_context.dart';
import 'app_logger.dart';

/// 移动端用户管理工具类
/// 提供简洁有效的用户数据管理功能
class UserManager {
  /// 清理当前用户的所有数据
  static Future<bool> clearCurrentUserData() async {
    try {
      await UserStorageContext.instance.clearCurrentUserData();
      AppLogger.info('当前用户数据清理完成');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('清理当前用户数据失败: $e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 切换用户（清理当前用户数据并设置新用户）
  /// [newUserPhone] 新用户手机号
  static Future<bool> switchUser(String newUserPhone) async {
    try {
      await UserStorageContext.instance.switchUser(newUserPhone);
      AppLogger.info('用户切换完成: $newUserPhone');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('用户切换失败: $e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 检查当前用户是否有本地数据
  static Future<bool> hasCurrentUserData() async {
    return await UserStorageContext.instance.hasCurrentUserData();
  }

  /// 获取当前用户手机号
  static String? getCurrentUserPhone() {
    return UserStorageContext.instance.currentUserPhone;
  }

  /// 检查是否有用户上下文
  static bool hasUserContext() {
    return UserStorageContext.instance.hasUserContext;
  }
}

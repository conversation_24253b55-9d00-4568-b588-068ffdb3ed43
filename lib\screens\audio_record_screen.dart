import 'package:flutter/material.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:provider/provider.dart';
import '../providers/device_provider.dart';
import '../services/device_service.dart';
import '../services/audio_recorder_service.dart';

class AudioRecordScreen extends StatefulWidget {
  const AudioRecordScreen({Key? key}) : super(key: key);

  @override
  _AudioRecordScreenState createState() => _AudioRecordScreenState();
}

class _AudioRecordScreenState extends State<AudioRecordScreen> {
  AudioRecorderService _audioService = AudioRecorderService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAudio();
    });
  }

  Future<void> _initializeAudio() async {
    // 设置状态变化回调
    _audioService.setStateChangeCallback(() {
      if (mounted) {
        setState(() {});
      }
    });
    await _audioService.init();
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }

  void _startRecording() {
    _audioService.startRecording();
  }

  void _stopRecording() {
    _audioService.stopRecording();
  }

  void _playRecording() {
    _audioService.playRecording();
  }

  void _stopPlayback() {
    _audioService.stopPlayback();
  }

  void _resetRecording() {
    _audioService.deleteRecording();
  }

  void _useRecording() async {
    final deviceService = Provider.of<DeviceApiService>(context, listen: false);
    final device = Provider.of<DeviceProvider>(context, listen: false).device;
    // 上传录音文件到服务器
    var result = await deviceService.deviceAudioUpload(
        device!.deviceName, _audioService.recordingPath);
    if (result) {
      // 发送命令更新设备的录音文件
      result = await deviceService.deviceAudioUpdate(device.deviceName);
      if (result) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('录音已采用，设备正在下载中....')),
        );
      } else {
        // 上传失败，删除录音文件
        await _audioService.deleteRecording();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('录音'),
      ),
      body: Column(
        children: [
          // 上部：录音/播放时间显示
          Expanded(
            flex: 2,
            child: Center(
              child: Text(
                _audioService.audioTime,
                style: TextStyle(fontSize: 48, fontWeight: FontWeight.bold),
              ),
            ),
          ),

          // 中部：波形显示
          Expanded(
            flex: 6,
            child: Center(
              child: _buildWaveform(),
            ),
          ),

          // 下部：按钮区域
          Expanded(
            flex: 2,
            child: Center(
              child: _buildControlButtons(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWaveform() {
    // 播放状态下显示播放波形
    if (_audioService.isPlaying && _audioService.hasRecorded) {
      return AudioFileWaveforms(
        size: Size(MediaQuery.of(context).size.width, 160),
        playerController: _audioService.playerController,
        enableSeekGesture: false,
        waveformType: WaveformType.long,
        playerWaveStyle: const PlayerWaveStyle(
          fixedWaveColor: Colors.blue,
          liveWaveColor: Colors.red,
          spacing: 6,
          scaleFactor: 200,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: const Color(0xFFF5F5F5),
        ),
        padding: const EdgeInsets.only(left: 18, right: 18),
      );
    }

    // 有录音文件但不在播放状态时，显示静态波形
    if (_audioService.hasRecorded && !_audioService.isPlaying) {
      return AudioFileWaveforms(
        size: Size(MediaQuery.of(context).size.width, 160),
        playerController: _audioService.playerController,
        enableSeekGesture: false,
        waveformType: WaveformType.fitWidth,
        playerWaveStyle: const PlayerWaveStyle(
          fixedWaveColor: Color.fromARGB(255, 107, 107, 107),
          liveWaveColor: Colors.blue,
          spacing: 6,
          scaleFactor: 200,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: const Color(0xFFF5F5F5),
        ),
        padding: const EdgeInsets.only(left: 18, right: 18),
      );
    }

    // 录音状态下或初始状态显示录音波形
    return AudioWaveforms(
      enableGesture: false,
      size: Size(MediaQuery.of(context).size.width, 160),
      recorderController: _audioService.recorderController,
      waveStyle: WaveStyle(
        waveColor: _audioService.isRecording ? Colors.red : Colors.blue,
        extendWaveform: true,
        showMiddleLine: false,
        scaleFactor: 70,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        color: const Color(0xFFF5F5F5),
      ),
      padding: const EdgeInsets.only(left: 18, right: 18),
    );
  }

  Widget _buildControlButtons() {
    // 录制完成后显示的三个按钮
    if (_audioService.hasRecorded) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 重录按钮
          IconButton(
            onPressed: _resetRecording,
            icon: Icon(Icons.refresh, size: 30),
            tooltip: '重录',
          ),

          // 试听按钮
          IconButton(
            onPressed: _audioService.isPlaying ? _stopPlayback : _playRecording,
            icon: Icon(
              _audioService.isPlaying ? Icons.stop : Icons.play_arrow,
              size: 50,
              color: Colors.blue,
            ),
            tooltip: _audioService.isPlaying ? '停止' : '播放',
          ),

          // 采用按钮
          IconButton(
            onPressed: _useRecording,
            icon: Icon(Icons.check_circle, size: 30, color: Colors.green),
            tooltip: '采用',
          ),
        ],
      );
    }

    // 录制前或录制中的单一按钮
    return IconButton(
      onPressed: _audioService.isRecording ? _stopRecording : _startRecording,
      icon: Icon(
        _audioService.isRecording ? Icons.stop_circle : Icons.mic,
        size: 70,
        color: _audioService.isRecording ? Colors.red : Colors.blue,
      ),
      tooltip: _audioService.isRecording ? '停止录音' : '开始录音',
    );
  }
}

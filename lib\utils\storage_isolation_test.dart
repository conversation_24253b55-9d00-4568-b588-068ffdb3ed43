import 'dart:convert';
import 'user_storage_context.dart';
import 'user_storage_manager.dart';
import 'app_storage.dart';
import 'app_logger.dart';
import '../models/device.dart';
import '../models/health_data.dart';
import '../models/qr_code.dart';
import '../models/ultrasonic_config.dart';

/// 数据隔离测试工具
/// 用于验证用户级别数据隔离机制的正确性
class StorageIsolationTest {
  static const String testUser1 = '13800138001';
  static const String testUser2 = '13800138002';

  /// 运行完整的数据隔离测试
  static Future<bool> runFullTest() async {
    AppLogger.info('开始运行数据隔离测试...');
    
    try {
      // 清理测试环境
      await _cleanupTestEnvironment();
      
      // 测试基础隔离功能
      final basicTest = await _testBasicIsolation();
      if (!basicTest) {
        AppLogger.error('基础隔离测试失败', error: Exception('基础隔离测试失败'));
        return false;
      }
      
      // 测试数据模型隔离
      final modelTest = await _testDataModelIsolation();
      if (!modelTest) {
        AppLogger.error('数据模型隔离测试失败', error: Exception('数据模型隔离测试失败'));
        return false;
      }
      
      // 测试用户切换
      final switchTest = await _testUserSwitching();
      if (!switchTest) {
        AppLogger.error('用户切换测试失败', error: Exception('用户切换测试失败'));
        return false;
      }
      
      // 测试数据清理
      final cleanupTest = await _testDataCleanup();
      if (!cleanupTest) {
        AppLogger.error('数据清理测试失败', error: Exception('数据清理测试失败'));
        return false;
      }
      
      // 清理测试环境
      await _cleanupTestEnvironment();
      
      AppLogger.info('所有数据隔离测试通过！');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('数据隔离测试异常: $e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 测试基础隔离功能
  static Future<bool> _testBasicIsolation() async {
    AppLogger.info('测试基础隔离功能...');
    
    // 设置用户1上下文
    UserStorageContext.instance.setCurrentUser(testUser1);
    await AppStorage.setUserString('test_key', 'user1_value');
    
    // 设置用户2上下文
    UserStorageContext.instance.setCurrentUser(testUser2);
    await AppStorage.setUserString('test_key', 'user2_value');
    
    // 验证用户1的数据
    UserStorageContext.instance.setCurrentUser(testUser1);
    final user1Value = await AppStorage.getUserString('test_key');
    if (user1Value != 'user1_value') {
      AppLogger.error('用户1数据不匹配: 期望 user1_value, 实际 $user1Value', 
                     error: Exception('数据不匹配'));
      return false;
    }
    
    // 验证用户2的数据
    UserStorageContext.instance.setCurrentUser(testUser2);
    final user2Value = await AppStorage.getUserString('test_key');
    if (user2Value != 'user2_value') {
      AppLogger.error('用户2数据不匹配: 期望 user2_value, 实际 $user2Value', 
                     error: Exception('数据不匹配'));
      return false;
    }
    
    AppLogger.info('基础隔离功能测试通过');
    return true;
  }

  /// 测试数据模型隔离
  static Future<bool> _testDataModelIsolation() async {
    AppLogger.info('测试数据模型隔离...');
    
    // 创建测试数据
    final testDevice1 = Device(
      bleMac: 'AA:BB:CC:DD:EE:F1',
      wifiConfig: true,
      isOnline: true,
      deviceName: 'TestDevice1',
      productId: '10000001',
      isSleep: false,
      wifiName: 'TestWiFi1',
      sleepStartTime: TimeOfDay(hour: 22, minute: 0),
      sleepEndTime: TimeOfDay(hour: 6, minute: 0),
    );
    
    final testDevice2 = Device(
      bleMac: 'AA:BB:CC:DD:EE:F2',
      wifiConfig: false,
      isOnline: false,
      deviceName: 'TestDevice2',
      productId: '10000002',
      isSleep: true,
      wifiName: 'TestWiFi2',
      sleepStartTime: TimeOfDay(hour: 23, minute: 0),
      sleepEndTime: TimeOfDay(hour: 7, minute: 0),
    );
    
    // 用户1保存设备
    UserStorageContext.instance.setCurrentUser(testUser1);
    await Device.saveDevice(testDevice1);
    
    // 用户2保存设备
    UserStorageContext.instance.setCurrentUser(testUser2);
    await Device.saveDevice(testDevice2);
    
    // 验证用户1的设备数据
    UserStorageContext.instance.setCurrentUser(testUser1);
    final retrievedDevice1 = await Device.getDevice();
    if (retrievedDevice1?.deviceName != 'TestDevice1') {
      AppLogger.error('用户1设备数据不匹配', error: Exception('设备数据不匹配'));
      return false;
    }
    
    // 验证用户2的设备数据
    UserStorageContext.instance.setCurrentUser(testUser2);
    final retrievedDevice2 = await Device.getDevice();
    if (retrievedDevice2?.deviceName != 'TestDevice2') {
      AppLogger.error('用户2设备数据不匹配', error: Exception('设备数据不匹配'));
      return false;
    }
    
    AppLogger.info('数据模型隔离测试通过');
    return true;
  }

  /// 测试用户切换
  static Future<bool> _testUserSwitching() async {
    AppLogger.info('测试用户切换...');
    
    // 设置初始用户和数据
    UserStorageContext.instance.setCurrentUser(testUser1);
    await AppStorage.setUserString('switch_test', 'original_data');
    
    // 切换到用户2
    await UserStorageContext.instance.switchUser(testUser2, clearCurrentData: false);
    
    // 验证用户2没有数据
    final user2Data = await AppStorage.getUserString('switch_test');
    if (user2Data != null) {
      AppLogger.error('用户切换后数据泄露', error: Exception('数据泄露'));
      return false;
    }
    
    // 切换回用户1
    await UserStorageContext.instance.switchUser(testUser1, clearCurrentData: false);
    
    // 验证用户1数据仍然存在
    final user1Data = await AppStorage.getUserString('switch_test');
    if (user1Data != 'original_data') {
      AppLogger.error('用户切换后数据丢失', error: Exception('数据丢失'));
      return false;
    }
    
    AppLogger.info('用户切换测试通过');
    return true;
  }

  /// 测试数据清理
  static Future<bool> _testDataCleanup() async {
    AppLogger.info('测试数据清理...');
    
    // 为两个用户创建数据
    UserStorageContext.instance.setCurrentUser(testUser1);
    await AppStorage.setUserString('cleanup_test', 'user1_data');
    
    UserStorageContext.instance.setCurrentUser(testUser2);
    await AppStorage.setUserString('cleanup_test', 'user2_data');
    
    // 清理用户1的数据
    UserStorageContext.instance.setCurrentUser(testUser1);
    await UserStorageContext.instance.clearCurrentUserData();
    
    // 验证用户1数据已清理
    final user1Data = await AppStorage.getUserString('cleanup_test');
    if (user1Data != null) {
      AppLogger.error('用户1数据清理失败', error: Exception('数据清理失败'));
      return false;
    }
    
    // 验证用户2数据仍然存在
    UserStorageContext.instance.setCurrentUser(testUser2);
    final user2Data = await AppStorage.getUserString('cleanup_test');
    if (user2Data != 'user2_data') {
      AppLogger.error('用户2数据意外丢失', error: Exception('数据意外丢失'));
      return false;
    }
    
    AppLogger.info('数据清理测试通过');
    return true;
  }

  /// 清理测试环境
  static Future<void> _cleanupTestEnvironment() async {
    try {
      // 清理测试用户数据
      await UserStorageManager.clearUserData(testUser1);
      await UserStorageManager.clearUserData(testUser2);
      
      // 重置用户上下文
      UserStorageContext.instance.reset();
      
      AppLogger.info('测试环境清理完成');
    } catch (e, stackTrace) {
      AppLogger.error('清理测试环境失败: $e', error: e, stackTrace: stackTrace);
    }
  }

  /// 生成测试报告
  static Future<Map<String, dynamic>> generateTestReport() async {
    final report = <String, dynamic>{};
    
    try {
      // 获取所有用户列表
      final allUsers = await UserStorageManager.getAllUserPhones();
      report['totalUsers'] = allUsers.length;
      report['userList'] = allUsers;
      
      // 获取每个用户的数据统计
      final userStats = <String, Map<String, int>>{};
      for (String userPhone in allUsers) {
        userStats[userPhone] = await UserStorageManager.getUserDataStats(userPhone);
      }
      report['userDataStats'] = userStats;
      
      // 当前用户信息
      report['currentUser'] = UserStorageContext.instance.currentUserPhone;
      report['hasCurrentUserContext'] = UserStorageContext.instance.hasUserContext;
      
      // 生成时间
      report['generatedAt'] = DateTime.now().toIso8601String();
      
      AppLogger.info('测试报告生成完成');
    } catch (e, stackTrace) {
      AppLogger.error('生成测试报告失败: $e', error: e, stackTrace: stackTrace);
      report['error'] = e.toString();
    }
    
    return report;
  }
}

import 'package:shared_preferences/shared_preferences.dart';
import 'app_logger.dart';

/// 用户数据隔离注册中心
class UserDataRegistry {
  static final Set<String> _userDataKeys = <String>{};

  /// 注册需要用户隔离的数据类型
  static void register(String dataKey) {
    _userDataKeys.add(dataKey);
    AppLogger.info('已注册用户隔离数据类型: $dataKey');
  }

  /// 获取所有已注册的用户数据类型
  static Set<String> get registeredKeys => Set.unmodifiable(_userDataKeys);

  /// 检查数据类型是否已注册
  static bool isRegistered(String dataKey) => _userDataKeys.contains(dataKey);
}

/// 用户存储工具类 - 简化版本，只保留核心功能
class UserStorage {
  /// 生成用户相关的存储键名
  /// [baseKey] 基础键名
  /// [userPhone] 用户手机号，如果为null则返回原始键名
  static String getUserKey(String baseKey, String? userPhone) {
    if (userPhone == null || userPhone.isEmpty) {
      return baseKey; // 向后兼容
    }
    return '${userPhone}_$baseKey';
  }

  /// 清理指定用户的所有数据
  static Future<void> clearUserData(String userPhone) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();

      for (String baseKey in UserDataRegistry.registeredKeys) {
        final userKey = getUserKey(baseKey, userPhone);
        await asyncPrefs.remove(userKey);
      }

      AppLogger.info('用户数据已清理: $userPhone (${UserDataRegistry.registeredKeys.length}个数据类型)');
    } catch (e, stackTrace) {
      AppLogger.error('清理用户数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 迁移现有数据到指定用户（用于兼容性）
  static Future<void> migrateDataToUser(String userPhone) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();

      for (String baseKey in UserDataRegistry.registeredKeys) {
        final oldData = await asyncPrefs.getString(baseKey);
        if (oldData != null) {
          final newKey = getUserKey(baseKey, userPhone);
          await asyncPrefs.setString(newKey, oldData);
          await asyncPrefs.remove(baseKey);
          AppLogger.info('数据迁移: $baseKey -> $newKey');
        }
      }
    } catch (e, stackTrace) {
      AppLogger.error('数据迁移失败: $e', error: e, stackTrace: stackTrace);
    }
  }

  /// 检查用户是否有数据
  static Future<bool> hasUserData(String userPhone) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();

      for (String baseKey in UserDataRegistry.registeredKeys) {
        final userKey = getUserKey(baseKey, userPhone);
        final data = await asyncPrefs.getString(userKey);
        if (data != null) return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }
}

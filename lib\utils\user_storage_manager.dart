import 'package:shared_preferences/shared_preferences.dart';
import 'app_logger.dart';
import 'user_storage_context.dart';

/// 用户存储管理器，负责处理用户级别的数据隔离
class UserStorageManager {
  /// 获取所有需要用户隔离的基础键名
  static List<String> _getBaseKeys() {
    return [
      'petDevice',
      'healthData',
      'qrCode',
      'ultrasonicConfigs',
      // 可以在这里添加新的需要用户隔离的数据类型
    ];
  }
  /// 生成用户相关的存储键名
  /// [baseKey] 基础键名（如 'petDevice', 'healthData'）
  /// [userPhone] 用户手机号，如果为null则使用默认键名
  static String getUserKey(String baseKey, String? userPhone) {
    if (userPhone == null || userPhone.isEmpty) {
      // 如果没有用户信息，返回原始键名以保持向后兼容性
      return baseKey;
    }
    // 使用用户手机号作为前缀，确保数据隔离
    return '${userPhone}_$baseKey';
  }

  /// 自动生成当前用户的存储键名
  /// [baseKey] 基础键名
  /// 通过UserStorageContext自动获取当前用户信息
  static String getAutoUserKey(String baseKey) {
    return UserStorageContext.instance.getUserKey(baseKey);
  }

  /// 清理指定用户的所有本地数据
  /// [userPhone] 要清理数据的用户手机号
  static Future<void> clearUserData(String userPhone) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();

      // 定义需要清理的基础键名列表
      final baseKeys = _getBaseKeys();

      // 清理每个用户相关的键
      for (String baseKey in baseKeys) {
        final userKey = getUserKey(baseKey, userPhone);
        await asyncPrefs.remove(userKey);
        AppLogger.info('已清理用户数据键: $userKey');
      }

      AppLogger.info('用户 $userPhone 的所有本地数据已清理完成');
    } catch (e, stackTrace) {
      AppLogger.error('清理用户数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 清理所有用户的本地数据（极端情况使用）
  static Future<void> clearAllUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      // 定义需要清理的基础键名模式
      final baseKeyPatterns = [
        '_petDevice',
        '_healthData',
        '_qrCode',
        '_ultrasonicConfigs',
      ];

      // 找到所有匹配的用户数据键并清理
      for (String key in keys) {
        for (String pattern in baseKeyPatterns) {
          if (key.contains(pattern)) {
            await prefs.remove(key);
            AppLogger.info('已清理数据键: $key');
            break;
          }
        }
      }

      AppLogger.info('所有用户数据已清理完成');
    } catch (e, stackTrace) {
      AppLogger.error('清理所有用户数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 迁移现有数据到当前用户（用于兼容性）
  /// [userPhone] 当前用户手机号
  static Future<void> migrateExistingDataToUser(String userPhone) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();

      // 定义需要迁移的基础键名
      final baseKeys = _getBaseKeys();

      for (String baseKey in baseKeys) {
        // 检查是否存在旧的数据
        final oldData = await asyncPrefs.getString(baseKey);
        if (oldData != null) {
          // 将数据迁移到用户专属键名
          final newKey = getUserKey(baseKey, userPhone);
          await asyncPrefs.setString(newKey, oldData);

          // 删除旧键名数据
          await asyncPrefs.remove(baseKey);

          AppLogger.info('已迁移数据: $baseKey -> $newKey');
        }
      }

      AppLogger.info('用户 $userPhone 的数据迁移完成');
    } catch (e, stackTrace) {
      AppLogger.error('数据迁移失败: $e', error: e, stackTrace: stackTrace);
      // 迁移失败不应该阻止应用运行，只记录错误
    }
  }

  /// 检查用户是否有本地数据
  /// [userPhone] 用户手机号
  static Future<bool> hasUserData(String userPhone) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();

      final baseKeys = _getBaseKeys();

      for (String baseKey in baseKeys) {
        final userKey = getUserKey(baseKey, userPhone);
        final data = await asyncPrefs.getString(userKey);
        if (data != null) {
          return true;
        }
      }

      return false;
    } catch (e, stackTrace) {
      AppLogger.error('检查用户数据失败: $e', error: e, stackTrace: stackTrace);
      return false;
    }
  }


}

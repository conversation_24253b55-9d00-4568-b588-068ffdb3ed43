/// 音频录制服务
///
/// 这个服务提供以下功能：
/// 1. 录制音频并实时显示波形
/// 2. 音频播放和波形可视化
/// 3. 支持iOS和Android平台
///
/// 使用方法：
/// ```dart
/// final audioService = AudioRecorderService();
/// await audioService.init();
///
/// // 开始录音
/// await audioService.startRecording();
///
/// // 停止录音
/// await audioService.stopRecording();
///
/// // 播放录音
/// await audioService.playRecording();
///
/// // 清理资源
/// audioService.dispose();
/// ```
///
/// 技术实现：
/// - 使用audio_waveforms包录制音频并提供实时波形显示
/// - 使用audio_waveforms包播放录制的音频
///
/// 配置参数：
/// - 采样率: 16000 Hz
/// - 最大录音时长: 15秒
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/app_logger.dart';

class AudioRecorderService {
  RecorderController _recorderController = RecorderController();
  PlayerController _playerController = PlayerController();
  Timer? _timer;
  String _recordingPath = '';
  bool _isRecording = false;
  bool _hasRecorded = false;
  bool _isPlaying = false;
  String _audioTime = "00:00";
  List<double> _waveformData = [];

  // 状态变化回调
  VoidCallback? _onStateChanged;

  // 最大录音时长（毫秒）
  static const int maxRecordingDuration = 15000;

  // Getters
  bool get isRecording => _isRecording;
  bool get hasRecorded => _hasRecorded;
  bool get isPlaying => _isPlaying;
  String get audioTime => _audioTime;
  List<double> get waveformData => _waveformData;
  String get recordingPath => _recordingPath;
  RecorderController get recorderController => _recorderController;
  PlayerController get playerController => _playerController;

  // 设置状态变化回调
  void setStateChangeCallback(VoidCallback callback) {
    _onStateChanged = callback;
  }

  // 通知状态变化
  void _notifyStateChanged() {
    _onStateChanged?.call();
  }

  Future<void> init() async {
    final tempDir = await getTemporaryDirectory();
    _recordingPath = '${tempDir.path}/recording.m4a';
    // 检查是否有已存在的录音文件
    if (await File(_recordingPath).exists()) {
      _hasRecorded = true;
      await _preparePlayerController();
    }
    _notifyStateChanged();
    // 检查录音权限
    await _recorderController.checkPermission();
    // 确保目录存在
    if (!await Directory(tempDir.path).exists()) {
      await Directory(tempDir.path).create(recursive: true);
    }
    // 设置音频播放完成监听
    _playerController.onCompletion.listen((_) async {
      _isPlaying = false;
      _timer?.cancel();
      _timer = null;
      // 重置音频时间显示到开始
      _audioTime = "00:00";
      _notifyStateChanged();
    });
    _playerController.setFinishMode(finishMode: FinishMode.pause);
  }

  Future<void> startRecording() async {
    if (_isRecording) return;

    try {
      // 删除之前的录音文件
      final file = File(_recordingPath);
      if (await file.exists()) {
        await file.delete();
      }

      // 重置录音时间
      _audioTime = "00:00";
      _hasRecorded = false;

      // 使用audio_waveforms录制（用于波形显示）
      await _recorderController.record(
        path: _recordingPath,
        androidEncoder: AndroidEncoder.aac,
        androidOutputFormat: AndroidOutputFormat.mpeg4,
        iosEncoder: IosEncoder.kAudioFormatMPEG4AAC,
        sampleRate: 16000,
        bitRate: 256000,
      );

      _isRecording = true;
      _notifyStateChanged();
      int tmpseconds = 0;
      // 启动计时器，每秒更新一次时间
      _timer = Timer.periodic(const Duration(milliseconds: 10), (timer) async {
        // 检查是否达到最大录音时长
        if (tmpseconds >= maxRecordingDuration) {
          stopRecording();
          return;
        }
        tmpseconds = tmpseconds + 10;
        // 更新录音时间
        final seconds1 = (tmpseconds ~/ 1000).toString().padLeft(2, '0');
        final seconds2 = ((tmpseconds % 1000) ~/ 10).toString().padLeft(2, '0');
        _audioTime = "$seconds1:$seconds2";
        _notifyStateChanged();
      });
    } catch (e, stackTrace) {
      AppLogger.warning('录音启动失败: $e', stackTrace: stackTrace);
      _isRecording = false;
      _notifyStateChanged();
    }
  }

  Future<void> stopRecording() async {
    if (!_isRecording) return;

    try {
      // 停止录音
      await _recorderController.stop();

      _isRecording = false;
      _hasRecorded = true;

      if (_timer != null) {
        _timer!.cancel();
        _timer = null;
      }
      _notifyStateChanged();
      await _preparePlayerController();
    } catch (e, stackTrace) {
      AppLogger.warning('停止录音失败: $e', stackTrace: stackTrace);
      _isRecording = false;
      _notifyStateChanged();
    }
  }

  Future<void> _preparePlayerController() async {
    try {
      // 检查文件是否存在
      final file = File(_recordingPath);
      if (!await file.exists()) {
        AppLogger.warning('录音文件不存在: $_recordingPath');
        return;
      }

      // 为播放准备波形数据
      await _playerController.preparePlayer(
        path: _recordingPath,
        noOfSamples: 100,
      );
      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('准备播放器失败: $e', stackTrace: stackTrace);
    }
  }

  Future<void> playRecording() async {
    if (!_hasRecorded || _isPlaying) return;

    try {
      // 检查文件是否存在
      final file = File(_recordingPath);
      if (!await file.exists()) {
        AppLogger.warning('录音文件不存在，无法播放');
        return;
      }
      final maxPlayDuration =
          await _playerController.getDuration(DurationType.max);
      // 使用PlayerController播放音频和显示波形
      await _playerController.startPlayer();
      _isPlaying = true;
      int tmpseconds = 0;
      _notifyStateChanged();
      // 启动计时器更新播放时间
      _timer = Timer.periodic(const Duration(milliseconds: 10), (timer) async {
        if (!_isPlaying || tmpseconds > maxPlayDuration) {
          timer.cancel();
          _timer = null;
          return;
        }
        tmpseconds = await _playerController.getDuration(DurationType.current);
        // 更新播放时间
        final seconds1 = (tmpseconds ~/ 1000).toString().padLeft(2, '0');
        final seconds2 = ((tmpseconds % 1000) ~/ 10).toString().padLeft(2, '0');
        _audioTime = "$seconds1:$seconds2";
        _notifyStateChanged();
      });
    } catch (e, stackTrace) {
      AppLogger.warning('播放录音失败: $e', stackTrace: stackTrace);
      _isPlaying = false;
      _notifyStateChanged();
    }
  }

  Future<void> stopPlayback() async {
    if (!_isPlaying) return;

    try {
      await _playerController.pausePlayer();
      _isPlaying = false;
      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('停止播放失败: $e', stackTrace: stackTrace);
    }
  }

  Future<void> deleteRecording() async {
    try {
      await stopPlayback();

      final file = File(_recordingPath);
      if (await file.exists()) {
        await file.delete();
      }

      _hasRecorded = false;
      _audioTime = "00:00";
      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('删除录音失败: $e', stackTrace: stackTrace);
    }
  }

  void dispose() {
    _timer?.cancel();
    _recorderController.dispose();
    _playerController.dispose();
  }
}

import 'app_logger.dart';
import 'user_storage_manager.dart';

/// 用户存储上下文管理器，提供透明的用户级别数据隔离
///
/// 使用单例模式管理当前用户状态，自动为存储操作生成用户相关的键名
class UserStorageContext {
  static UserStorageContext? _instance;
  String? _currentUserPhone;

  UserStorageContext._();

  /// 获取单例实例
  static UserStorageContext get instance {
    _instance ??= UserStorageContext._();
    return _instance!;
  }

  /// 获取当前用户手机号
  String? get currentUserPhone => _currentUserPhone;

  /// 设置当前用户
  /// [userPhone] 用户手机号，设置为null表示清除用户上下文
  void setCurrentUser(String? userPhone) {
    _currentUserPhone = userPhone;
    AppLogger.info('用户存储上下文已更新: ${userPhone ?? "已清除"}');
  }

  /// 生成用户相关的存储键名
  /// [baseKey] 基础键名（如 'petDevice', 'healthData'）
  /// 返回用户专属的存储键名，如果没有用户上下文则返回原始键名
  String getUserKey(String baseKey) {
    if (_currentUserPhone == null || _currentUserPhone!.isEmpty) {
      // 没有用户上下文时使用原始键名，保持向后兼容性
      return baseKey;
    }
    // 使用用户手机号作为前缀，确保数据隔离
    return '${_currentUserPhone}_$baseKey';
  }

  /// 检查是否有用户上下文
  bool get hasUserContext =>
      _currentUserPhone != null && _currentUserPhone!.isNotEmpty;

  /// 清理当前用户的所有本地数据
  Future<void> clearCurrentUserData() async {
    if (!hasUserContext) {
      AppLogger.warning('尝试清理用户数据但没有用户上下文');
      return;
    }

    try {
      // 调用UserStorageManager来执行清理
      await UserStorageManager.clearUserData(_currentUserPhone!);
      AppLogger.info('当前用户数据已清理: $_currentUserPhone');
    } catch (e, stackTrace) {
      AppLogger.error(
        '清理当前用户数据失败: $e',
        error: e,
        stackTrace: stackTrace,
      );
      // 清理失败不应该阻止用户操作，只记录错误
    }
  }

  /// 迁移现有数据到当前用户（用于兼容性）
  Future<void> migrateExistingDataToCurrentUser() async {
    if (!hasUserContext) {
      AppLogger.warning('尝试迁移数据但没有用户上下文');
      return;
    }

    try {
      // 调用UserStorageManager来执行迁移
      await UserStorageManager.migrateExistingDataToUser(_currentUserPhone!);
      AppLogger.info('现有数据已迁移到当前用户: $_currentUserPhone');
    } catch (e, stackTrace) {
      AppLogger.error(
        '数据迁移失败: $e',
        error: e,
        stackTrace: stackTrace,
      );
      // 迁移失败不应该阻止应用运行，只记录错误
    }
  }

  /// 重置用户存储上下文（用于测试）
  void reset() {
    _currentUserPhone = null;
    AppLogger.info('用户存储上下文已重置');
  }

  /// 切换到指定用户（包含数据清理和迁移）
  /// [newUserPhone] 新用户手机号
  /// [clearCurrentData] 是否清理当前用户数据，默认为true
  Future<void> switchUser(String newUserPhone, {bool clearCurrentData = true}) async {
    final oldUserPhone = _currentUserPhone;

    try {
      // 如果需要清理当前用户数据
      if (clearCurrentData && hasUserContext) {
        await clearCurrentUserData();
      }

      // 设置新用户上下文
      setCurrentUser(newUserPhone);

      // 迁移现有数据到新用户（如果有的话）
      await migrateExistingDataToCurrentUser();

      AppLogger.info('用户切换完成: ${oldUserPhone ?? "无"} -> $newUserPhone');
    } catch (e, stackTrace) {
      AppLogger.error(
        '用户切换失败: $e',
        error: e,
        stackTrace: stackTrace,
      );
      // 切换失败时恢复原用户上下文
      _currentUserPhone = oldUserPhone;
      rethrow;
    }
  }

  /// 获取当前用户的数据统计
  Future<Map<String, int>> getCurrentUserDataStats() async {
    if (!hasUserContext) {
      return {};
    }
    return await UserStorageManager.getUserDataStats(_currentUserPhone!);
  }

  /// 备份当前用户数据
  Future<Map<String, dynamic>?> backupCurrentUserData() async {
    if (!hasUserContext) {
      AppLogger.warning('尝试备份数据但没有用户上下文');
      return null;
    }
    return await UserStorageManager.backupUserData(_currentUserPhone!);
  }

  /// 检查当前用户是否有本地数据
  Future<bool> hasCurrentUserData() async {
    if (!hasUserContext) {
      return false;
    }
    return await UserStorageManager.hasUserData(_currentUserPhone!);
  }
}

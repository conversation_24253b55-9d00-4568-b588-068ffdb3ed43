import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class QRScanner extends StatefulWidget {
  final Function(String) onDetect;

  const QRScanner({Key? key, required this.onDetect}) : super(key: key);

  @override
  State<QRScanner> createState() => _QRScannerState();
}

class _QRScannerState extends State<QRScanner>
    with SingleTickerProviderStateMixin {
  late MobileScannerController _controller;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.normal,
      facing: CameraFacing.back,
    );

    // 初始化扫描线动画
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);

    // 循环播放动画
    _animationController.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final scanAreaWidth = screenSize.width * 0.9;
    final scanAreaHeight = scanAreaWidth * 1.2; // 拉长扫描框
    final scanAreaTop = (screenSize.height - scanAreaHeight) / 2 - 100; // 上移更多
    final scanAreaBottom = scanAreaTop + scanAreaHeight;

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 相机预览
          MobileScanner(
            controller: _controller,
            onDetect: (capture) {
              final List<Barcode> barcodes = capture.barcodes;
              if (barcodes.isNotEmpty) {
                final String? code = barcodes.first.rawValue;
                if (code != null) {
                  widget.onDetect(code);
                }
              }
            },
          ),

          // 遮罩层，创建扫描窗口效果
          Stack(
            children: [
              // 上方遮罩
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                height: scanAreaTop,
                child: Container(color: Colors.black.withOpacity(0.8)),
              ),
              // 下方遮罩（收缩的黑边）
              Positioned(
                top: scanAreaBottom,
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(color: Colors.black.withOpacity(0.8)),
              ),
              // 左侧遮罩
              Positioned(
                top: scanAreaTop,
                left: 0,
                width: (screenSize.width - scanAreaWidth) / 2,
                height: scanAreaHeight,
                child: Container(color: Colors.black.withOpacity(0.8)),
              ),
              // 右侧遮罩
              Positioned(
                top: scanAreaTop,
                right: 0,
                width: (screenSize.width - scanAreaWidth) / 2,
                height: scanAreaHeight,
                child: Container(color: Colors.black.withOpacity(0.8)),
              ),
            ],
          ),

          // 扫描框
          Positioned(
            top: scanAreaTop,
            left: (screenSize.width - scanAreaWidth) / 2,
            child: Container(
              width: scanAreaWidth,
              height: scanAreaHeight,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.white,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Stack(
                children: [
                  // 四个角的装饰
                  ...List.generate(4, (index) {
                    final isTop = index < 2;
                    final isLeft = index % 2 == 0;
                    return Positioned(
                      top: isTop ? 8 : null,
                      bottom: isTop ? null : 8,
                      left: isLeft ? 8 : null,
                      right: isLeft ? null : 8,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          border: Border(
                            top: isTop
                                ? const BorderSide(
                                    color: Colors.green, width: 3)
                                : BorderSide.none,
                            bottom: !isTop
                                ? const BorderSide(
                                    color: Colors.green, width: 3)
                                : BorderSide.none,
                            left: isLeft
                                ? const BorderSide(
                                    color: Colors.green, width: 3)
                                : BorderSide.none,
                            right: !isLeft
                                ? const BorderSide(
                                    color: Colors.green, width: 3)
                                : BorderSide.none,
                          ),
                        ),
                      ),
                    );
                  }),

                  // 扫描线动画
                  AnimatedBuilder(
                    animation: _animation,
                    builder: (context, child) {
                      return Positioned(
                        top: _animation.value * (scanAreaHeight - 4),
                        left: 2,
                        right: 2,
                        child: Container(
                          height: 2,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.transparent,
                                Colors.green,
                                Colors.green,
                                Colors.transparent,
                              ],
                              stops: const [0.0, 0.3, 0.7, 1.0],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.green.withOpacity(0.5),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          // 提示文字
          Positioned(
            top: scanAreaTop - 60,
            left: 0,
            right: 0,
            child: const Text(
              '将二维码放入框内进行扫描',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // 控制按钮（位于收缩的黑边中）
          Positioned(
            bottom: 60,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: IconButton(
                    color: Colors.white,
                    icon: const Icon(Icons.flash_on, size: 28),
                    onPressed: () => _controller.toggleTorch(),
                  ),
                ),
                const SizedBox(width: 40),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: IconButton(
                    color: Colors.white,
                    icon: const Icon(Icons.flip_camera_ios, size: 28),
                    onPressed: () => _controller.switchCamera(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

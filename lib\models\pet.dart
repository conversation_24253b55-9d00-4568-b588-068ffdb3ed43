class Pet {
  final String name;
  final String species;
  final String breed;
  final String birthday;
  final int weight;
  final int height;

  // 添加其他宠物字段

  Pet({
    required this.name,
    required this.species,
    required this.breed,
    required this.birthday,
    required this.weight,
    required this.height,
  });

  factory Pet.fromJson(Map<String, dynamic> json) {
    return Pet(
      name: json['name'],
      species: json['species'],
      breed: json['breed'],
      birthday: json['birthday'],
      weight: json['weight'],
      height: json['height'],
    );
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'species': species,
        'breed': breed,
        'birthday': birthday,
        'weight': weight,
        'height': height,
      };
}

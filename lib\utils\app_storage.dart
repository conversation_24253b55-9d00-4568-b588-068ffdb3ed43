import 'package:shared_preferences/shared_preferences.dart';
import 'user_storage_manager.dart';
import 'app_logger.dart';

/// 应用存储抽象层，提供统一的存储接口
/// 自动处理用户级别数据隔离和应用级别数据管理
class AppStorage {
  static const String _appPrefix = 'app_';
  
  /// 存储用户级别的数据（会自动添加用户前缀）
  static Future<void> setUserString(String key, String value) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserStorageManager.getAutoUserKey(key);
      await asyncPrefs.setString(storageKey, value);
      AppLogger.info('用户数据已保存: $storageKey');
    } catch (e, stackTrace) {
      AppLogger.error('保存用户数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取用户级别的数据
  static Future<String?> getUserString(String key) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserStorageManager.getAutoUserKey(key);
      return await asyncPrefs.getString(storageKey);
    } catch (e, stackTrace) {
      AppLogger.error('获取用户数据失败: $e', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 存储用户级别的布尔值
  static Future<void> setUserBool(String key, bool value) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserStorageManager.getAutoUserKey(key);
      await asyncPrefs.setBool(storageKey, value);
      AppLogger.info('用户布尔数据已保存: $storageKey = $value');
    } catch (e, stackTrace) {
      AppLogger.error('保存用户布尔数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取用户级别的布尔值
  static Future<bool> getUserBool(String key, {bool defaultValue = false}) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserStorageManager.getAutoUserKey(key);
      return await asyncPrefs.getBool(storageKey) ?? defaultValue;
    } catch (e, stackTrace) {
      AppLogger.error('获取用户布尔数据失败: $e', error: e, stackTrace: stackTrace);
      return defaultValue;
    }
  }

  /// 存储用户级别的整数
  static Future<void> setUserInt(String key, int value) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserStorageManager.getAutoUserKey(key);
      await asyncPrefs.setInt(storageKey, value);
      AppLogger.info('用户整数数据已保存: $storageKey = $value');
    } catch (e, stackTrace) {
      AppLogger.error('保存用户整数数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取用户级别的整数
  static Future<int> getUserInt(String key, {int defaultValue = 0}) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserStorageManager.getAutoUserKey(key);
      return await asyncPrefs.getInt(storageKey) ?? defaultValue;
    } catch (e, stackTrace) {
      AppLogger.error('获取用户整数数据失败: $e', error: e, stackTrace: stackTrace);
      return defaultValue;
    }
  }

  /// 删除用户级别的数据
  static Future<void> removeUserData(String key) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserStorageManager.getAutoUserKey(key);
      await asyncPrefs.remove(storageKey);
      AppLogger.info('用户数据已删除: $storageKey');
    } catch (e, stackTrace) {
      AppLogger.error('删除用户数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 存储应用级别的数据（不会添加用户前缀，全局共享）
  static Future<void> setAppString(String key, String value) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final appKey = '$_appPrefix$key';
      await asyncPrefs.setString(appKey, value);
      AppLogger.info('应用数据已保存: $appKey');
    } catch (e, stackTrace) {
      AppLogger.error('保存应用数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取应用级别的数据
  static Future<String?> getAppString(String key) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final appKey = '$_appPrefix$key';
      return await asyncPrefs.getString(appKey);
    } catch (e, stackTrace) {
      AppLogger.error('获取应用数据失败: $e', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 存储应用级别的布尔值
  static Future<void> setAppBool(String key, bool value) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final appKey = '$_appPrefix$key';
      await asyncPrefs.setBool(appKey, value);
      AppLogger.info('应用布尔数据已保存: $appKey = $value');
    } catch (e, stackTrace) {
      AppLogger.error('保存应用布尔数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取应用级别的布尔值
  static Future<bool> getAppBool(String key, {bool defaultValue = false}) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final appKey = '$_appPrefix$key';
      return await asyncPrefs.getBool(appKey) ?? defaultValue;
    } catch (e, stackTrace) {
      AppLogger.error('获取应用布尔数据失败: $e', error: e, stackTrace: stackTrace);
      return defaultValue;
    }
  }

  /// 存储应用级别的整数
  static Future<void> setAppInt(String key, int value) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final appKey = '$_appPrefix$key';
      await asyncPrefs.setInt(appKey, value);
      AppLogger.info('应用整数数据已保存: $appKey = $value');
    } catch (e, stackTrace) {
      AppLogger.error('保存应用整数数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取应用级别的整数
  static Future<int> getAppInt(String key, {int defaultValue = 0}) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final appKey = '$_appPrefix$key';
      return await asyncPrefs.getInt(appKey) ?? defaultValue;
    } catch (e, stackTrace) {
      AppLogger.error('获取应用整数数据失败: $e', error: e, stackTrace: stackTrace);
      return defaultValue;
    }
  }

  /// 删除应用级别的数据
  static Future<void> removeAppData(String key) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final appKey = '$_appPrefix$key';
      await asyncPrefs.remove(appKey);
      AppLogger.info('应用数据已删除: $appKey');
    } catch (e, stackTrace) {
      AppLogger.error('删除应用数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 检查用户是否有特定的数据
  static Future<bool> hasUserData(String key) async {
    try {
      final data = await getUserString(key);
      return data != null;
    } catch (e) {
      return false;
    }
  }

  /// 检查应用是否有特定的数据
  static Future<bool> hasAppData(String key) async {
    try {
      final data = await getAppString(key);
      return data != null;
    } catch (e) {
      return false;
    }
  }
}

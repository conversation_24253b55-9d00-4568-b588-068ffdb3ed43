import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../utils/user_context.dart';

class UltrasonicConfig {
  final int index;
  double frequency;
  double duration;
  bool isEnabled;

  UltrasonicConfig({
    required this.index,
    required this.frequency,
    required this.duration,
    required this.isEnabled,
  });

  // 从JSON创建模型
  factory UltrasonicConfig.fromJson(Map<String, dynamic> json) {
    return UltrasonicConfig(
      index: json['index'] as int,
      frequency: (json['frequency'] as num).toDouble(),
      duration: (json['duration'] as num).toDouble(),
      isEnabled: json['isEnabled'] as bool,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'frequency': frequency,
      'duration': duration,
      'isEnabled': isEnabled,
    };
  }

  static Future<void> saveConfig(List<UltrasonicConfig> configs) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey('ultrasonicConfigs');
    String jsonString = jsonEncode(configs.map((config) => config.toJson()).toList());
    await asyncPrefs.setString(storageKey, jsonString);
  }

  static Future<List<UltrasonicConfig>> getConfig() async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey('ultrasonicConfigs');
    String? jsonString = await asyncPrefs.getString(storageKey);
    if (jsonString != null) {
      List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList
          .map((config) => UltrasonicConfig.fromJson(config))
          .toList()
          .cast<UltrasonicConfig>();
    }
    return [];
  }

  // 创建副本
  UltrasonicConfig copy() {
    return UltrasonicConfig(
      index: index,
      frequency: frequency,
      duration: duration,
      isEnabled: isEnabled,
    );
  }
}

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/user_context.dart';

class QRCode {
  final String url;
  final String contactPhone;
  final String note;

  QRCode({
    required this.url,
    required this.contactPhone,
    required this.note,
  });

  factory QRCode.fromJson(Map<String, dynamic> json) {
    return QRCode(
      url: json['url'] as String,
      contactPhone: json['contactPhone'] as String,
      note: json['note'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'contactPhone': contactPhone,
      'note': note,
    };
  }

  static Future<void> saveQRCode(List<QRCode> qrCodes,
      {String key = 'qrCode'}) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey(key);
    String jsonString = jsonEncode(qrCodes.map((qrCode) => qrCode.toJson()).toList());
    await asyncPrefs.setString(storageKey, jsonString);
  }

  static Future<List<QRCode>> getQRCode({String key = 'qrCode'}) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey(key);
    String? jsonString = await asyncPrefs.getString(storageKey);
    if (jsonString != null) {
      List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList
          .map((qrCode) => QRCode.fromJson(qrCode))
          .toList()
          .cast<QRCode>();
    }
    return [];
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}

# Generated code do not commit.
file(TO_CMAKE_PATH "F:\\Flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "G:\\FlutterCode\\APPCODE\\PET_APP\\pet_care_copy" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=F:\\Flutter\\flutter"
  "PROJECT_DIR=G:\\FlutterCode\\APPCODE\\PET_APP\\pet_care_copy"
  "FLUTTER_ROOT=F:\\Flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=G:\\FlutterCode\\APPCODE\\PET_APP\\pet_care_copy\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=G:\\FlutterCode\\APPCODE\\PET_APP\\pet_care_copy"
  "FLUTTER_TARGET=G:\\FlutterCode\\APPCODE\\PET_APP\\pet_care_copy\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=G:\\FlutterCode\\APPCODE\\PET_APP\\pet_care_copy\\.dart_tool\\package_config.json"
)

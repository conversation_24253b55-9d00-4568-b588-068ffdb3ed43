import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'user_storage_context.dart';
import 'user_storage_manager.dart';
import 'app_logger.dart';

/// 用户管理工具类
/// 提供用户级别的高级管理功能，包括多用户支持、数据迁移等
class UserManager {
  /// 获取所有本地用户列表
  static Future<List<UserInfo>> getAllLocalUsers() async {
    try {
      final userPhones = await UserStorageManager.getAllUserPhones();
      final users = <UserInfo>[];
      
      for (String phone in userPhones) {
        final hasData = await UserStorageManager.hasUserData(phone);
        final stats = await UserStorageManager.getUserDataStats(phone);
        final totalDataSize = stats.values.fold(0, (sum, size) => sum + size);
        
        users.add(UserInfo(
          phone: phone,
          hasData: hasData,
          dataStats: stats,
          totalDataSize: totalDataSize,
          lastAccessTime: await _getLastAccessTime(phone),
        ));
      }
      
      // 按最后访问时间排序
      users.sort((a, b) => (b.lastAccessTime ?? DateTime(1970))
          .compareTo(a.lastAccessTime ?? DateTime(1970)));
      
      return users;
    } catch (e, stackTrace) {
      AppLogger.error('获取本地用户列表失败: $e', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  /// 清理指定用户的所有数据
  /// [userPhone] 用户手机号
  /// [keepUserRecord] 是否保留用户记录（仅清理数据）
  static Future<bool> cleanupUser(String userPhone, {bool keepUserRecord = false}) async {
    try {
      // 清理用户数据
      await UserStorageManager.clearUserData(userPhone);
      
      if (!keepUserRecord) {
        // 清理用户访问记录
        await _removeLastAccessTime(userPhone);
      }
      
      // 如果是当前用户，清除上下文
      if (UserStorageContext.instance.currentUserPhone == userPhone) {
        UserStorageContext.instance.setCurrentUser(null);
      }
      
      AppLogger.info('用户清理完成: $userPhone');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('清理用户失败: $e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 清理所有非活跃用户数据
  /// [daysThreshold] 天数阈值，超过此天数未访问的用户将被清理
  static Future<int> cleanupInactiveUsers(int daysThreshold) async {
    try {
      final users = await getAllLocalUsers();
      final cutoffDate = DateTime.now().subtract(Duration(days: daysThreshold));
      int cleanedCount = 0;
      
      for (UserInfo user in users) {
        // 跳过当前用户
        if (user.phone == UserStorageContext.instance.currentUserPhone) {
          continue;
        }
        
        // 检查是否超过阈值
        if (user.lastAccessTime == null || user.lastAccessTime!.isBefore(cutoffDate)) {
          final success = await cleanupUser(user.phone);
          if (success) {
            cleanedCount++;
            AppLogger.info('已清理非活跃用户: ${user.phone}');
          }
        }
      }
      
      AppLogger.info('非活跃用户清理完成，共清理 $cleanedCount 个用户');
      return cleanedCount;
    } catch (e, stackTrace) {
      AppLogger.error('清理非活跃用户失败: $e', error: e, stackTrace: stackTrace);
      return 0;
    }
  }

  /// 迁移用户数据到新手机号
  /// [oldPhone] 旧手机号
  /// [newPhone] 新手机号
  /// [deleteOldData] 是否删除旧数据
  static Future<bool> migrateUserData(String oldPhone, String newPhone, {bool deleteOldData = true}) async {
    try {
      // 备份旧用户数据
      final backup = await UserStorageManager.backupUserData(oldPhone);
      if (backup == null) {
        AppLogger.warning('用户 $oldPhone 没有数据需要迁移');
        return true;
      }
      
      // 恢复到新用户
      final success = await UserStorageManager.restoreUserData(backup, newPhone);
      if (!success) {
        AppLogger.error('数据迁移失败: $oldPhone -> $newPhone', error: Exception('恢复数据失败'));
        return false;
      }
      
      // 更新访问时间
      await _updateLastAccessTime(newPhone);
      
      // 删除旧数据（如果需要）
      if (deleteOldData) {
        await UserStorageManager.clearUserData(oldPhone);
        await _removeLastAccessTime(oldPhone);
      }
      
      // 如果当前用户是旧用户，更新上下文
      if (UserStorageContext.instance.currentUserPhone == oldPhone) {
        UserStorageContext.instance.setCurrentUser(newPhone);
      }
      
      AppLogger.info('用户数据迁移完成: $oldPhone -> $newPhone');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('用户数据迁移失败: $e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 导出用户数据
  /// [userPhone] 用户手机号
  static Future<String?> exportUserData(String userPhone) async {
    try {
      final backup = await UserStorageManager.backupUserData(userPhone);
      if (backup == null) {
        return null;
      }
      
      return jsonEncode(backup);
    } catch (e, stackTrace) {
      AppLogger.error('导出用户数据失败: $e', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 导入用户数据
  /// [jsonData] JSON格式的用户数据
  /// [targetUserPhone] 目标用户手机号
  static Future<bool> importUserData(String jsonData, String targetUserPhone) async {
    try {
      final backup = jsonDecode(jsonData) as Map<String, dynamic>;
      return await UserStorageManager.restoreUserData(backup, targetUserPhone);
    } catch (e, stackTrace) {
      AppLogger.error('导入用户数据失败: $e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 更新用户最后访问时间
  static Future<void> updateCurrentUserAccessTime() async {
    final currentUser = UserStorageContext.instance.currentUserPhone;
    if (currentUser != null) {
      await _updateLastAccessTime(currentUser);
    }
  }

  /// 获取存储使用统计
  static Future<StorageStats> getStorageStats() async {
    try {
      final users = await getAllLocalUsers();
      int totalUsers = users.length;
      int totalDataSize = users.fold(0, (sum, user) => sum + user.totalDataSize);
      
      final currentUser = UserStorageContext.instance.currentUserPhone;
      final currentUserStats = currentUser != null 
          ? await UserStorageManager.getUserDataStats(currentUser)
          : <String, int>{};
      
      return StorageStats(
        totalUsers: totalUsers,
        totalDataSize: totalDataSize,
        currentUser: currentUser,
        currentUserDataSize: currentUserStats.values.fold(0, (sum, size) => sum + size),
        userList: users,
      );
    } catch (e, stackTrace) {
      AppLogger.error('获取存储统计失败: $e', error: e, stackTrace: stackTrace);
      return StorageStats(
        totalUsers: 0,
        totalDataSize: 0,
        currentUser: null,
        currentUserDataSize: 0,
        userList: [],
      );
    }
  }

  /// 更新最后访问时间
  static Future<void> _updateLastAccessTime(String userPhone) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final key = 'last_access_$userPhone';
      await asyncPrefs.setString(key, DateTime.now().toIso8601String());
    } catch (e) {
      // 访问时间更新失败不应该影响主要功能
      AppLogger.warning('更新访问时间失败: $e');
    }
  }

  /// 获取最后访问时间
  static Future<DateTime?> _getLastAccessTime(String userPhone) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final key = 'last_access_$userPhone';
      final timeString = await asyncPrefs.getString(key);
      if (timeString != null) {
        return DateTime.tryParse(timeString);
      }
    } catch (e) {
      // 获取访问时间失败不应该影响主要功能
    }
    return null;
  }

  /// 删除最后访问时间记录
  static Future<void> _removeLastAccessTime(String userPhone) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final key = 'last_access_$userPhone';
      await asyncPrefs.remove(key);
    } catch (e) {
      // 删除访问时间失败不应该影响主要功能
    }
  }
}

/// 用户信息类
class UserInfo {
  final String phone;
  final bool hasData;
  final Map<String, int> dataStats;
  final int totalDataSize;
  final DateTime? lastAccessTime;

  UserInfo({
    required this.phone,
    required this.hasData,
    required this.dataStats,
    required this.totalDataSize,
    this.lastAccessTime,
  });

  Map<String, dynamic> toJson() => {
    'phone': phone,
    'hasData': hasData,
    'dataStats': dataStats,
    'totalDataSize': totalDataSize,
    'lastAccessTime': lastAccessTime?.toIso8601String(),
  };
}

/// 存储统计信息类
class StorageStats {
  final int totalUsers;
  final int totalDataSize;
  final String? currentUser;
  final int currentUserDataSize;
  final List<UserInfo> userList;

  StorageStats({
    required this.totalUsers,
    required this.totalDataSize,
    required this.currentUser,
    required this.currentUserDataSize,
    required this.userList,
  });

  Map<String, dynamic> toJson() => {
    'totalUsers': totalUsers,
    'totalDataSize': totalDataSize,
    'currentUser': currentUser,
    'currentUserDataSize': currentUserDataSize,
    'userList': userList.map((user) => user.toJson()).toList(),
  };
}
